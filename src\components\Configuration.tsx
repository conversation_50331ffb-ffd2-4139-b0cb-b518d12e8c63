import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, EyeOff, Check, X, RefreshCw } from 'lucide-react';
import { useConfig } from '../contexts/ConfigContext';
import { useToast } from './Toast';
import type { Service } from '../services/renderApi';

interface ConfigurationProps {
  isOpen: boolean;
  onClose: () => void;
}

const Configuration: React.FC<ConfigurationProps> = ({ isOpen, onClose }) => {
  const {
    apiKey,
    setApiKey,
    renderApi,
    selectedServices,
    setSelectedServices,
    autoRefresh,
    setAutoRefresh,
    refreshInterval,
    setRefreshInterval,
  } = useConfig();

  const { addToast } = useToast();

  const [tempApiKey, setTempApiKey] = useState(apiKey);
  const [showApiKey, setShowApiKey] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [services, setServices] = useState<Service[]>([]);
  const [isLoadingServices, setIsLoadingServices] = useState(false);
  const [tempRefreshInterval, setTempRefreshInterval] = useState(refreshInterval);

  useEffect(() => {
    setTempApiKey(apiKey);
  }, [apiKey]);

  useEffect(() => {
    setTempRefreshInterval(refreshInterval);
  }, [refreshInterval]);

  const testConnection = async () => {
    const keyToTest = tempApiKey.trim() || import.meta.env.VITE_RENDER_API_KEY;

    if (!keyToTest) {
      setConnectionStatus('error');
      addToast({
        type: 'error',
        title: 'No API key',
        message: 'Please provide a Render API key.',
      });
      return;
    }

    setIsTestingConnection(true);
    setConnectionStatus('idle');

    try {
      const testApi = renderApi || new (await import('../services/renderApi')).default({ apiKey: keyToTest });
      const isConnected = await testApi.testConnection();
      
      if (isConnected) {
        setConnectionStatus('success');
        addToast({
          type: 'success',
          title: 'Connection successful!',
          message: 'Your Render API key is valid.',
        });
        loadServices();
      } else {
        setConnectionStatus('error');
        addToast({
          type: 'error',
          title: 'Connection failed',
          message: 'Please check your API key and try again.',
        });
      }
    } catch (error) {
      setConnectionStatus('error');
      addToast({
        type: 'error',
        title: 'Connection failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred.',
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const loadServices = async () => {
    if (!renderApi) return;

    setIsLoadingServices(true);
    try {
      const fetchedServices = await renderApi.getServices();
      setServices(fetchedServices);
    } catch (error) {
      console.error('Error loading services:', error);
    } finally {
      setIsLoadingServices(false);
    }
  };

  const handleSave = () => {
    setApiKey(tempApiKey);
    setRefreshInterval(tempRefreshInterval);
    addToast({
      type: 'success',
      title: 'Settings saved',
      message: 'Your configuration has been updated successfully.',
    });
    onClose();
  };

  const handleServiceToggle = (serviceId: string) => {
    const newSelected = selectedServices.includes(serviceId)
      ? selectedServices.filter(id => id !== serviceId)
      : [...selectedServices, serviceId];
    setSelectedServices(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedServices.length === services.length) {
      setSelectedServices([]);
    } else {
      setSelectedServices(services.map(s => s.id));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Settings className="w-6 h-6" />
              <h2 className="text-xl font-semibold">Configuration</h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* API Key Section */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Render API Key
            </label>
            {import.meta.env.VITE_RENDER_API_KEY && !tempApiKey && (
              <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">
                  API key loaded from environment variables
                </p>
              </div>
            )}
            <div className="flex gap-2">
              <div className="relative flex-1">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  value={tempApiKey}
                  onChange={(e) => setTempApiKey(e.target.value)}
                  placeholder={
                    import.meta.env.VITE_RENDER_API_KEY
                      ? "Using environment API key (override here)"
                      : "Enter your Render API key"
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              <button
                onClick={testConnection}
                disabled={isTestingConnection || (!tempApiKey.trim() && !import.meta.env.VITE_RENDER_API_KEY)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isTestingConnection ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  'Test'
                )}
              </button>
            </div>
            
            {connectionStatus === 'success' && (
              <div className="mt-2 flex items-center gap-2 text-green-600">
                <Check className="w-4 h-4" />
                <span className="text-sm">Connection successful!</span>
              </div>
            )}
            
            {connectionStatus === 'error' && (
              <div className="mt-2 flex items-center gap-2 text-red-600">
                <X className="w-4 h-4" />
                <span className="text-sm">Connection failed. Please check your API key.</span>
              </div>
            )}
          </div>

          {/* Services Selection */}
          {services.length > 0 && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Select Services to Monitor
                </label>
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {selectedServices.length === services.length ? 'Deselect All' : 'Select All'}
                </button>
              </div>
              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-md">
                {services.map((service) => (
                  <label
                    key={service.id}
                    className="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                  >
                    <input
                      type="checkbox"
                      checked={selectedServices.includes(service.id)}
                      onChange={() => handleServiceToggle(service.id)}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium">{service.name}</div>
                      <div className="text-sm text-gray-500">
                        {service.type} • {service.status} • {service.region}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Refresh Settings */}
          <div className="mb-6">
            <label className="flex items-center gap-2 mb-3">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Auto-refresh logs</span>
            </label>
            
            {autoRefresh && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Refresh Interval (seconds)
                </label>
                <input
                  type="number"
                  min="1"
                  max="60"
                  value={tempRefreshInterval / 1000}
                  onChange={(e) => setTempRefreshInterval(parseInt(e.target.value) * 1000)}
                  className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Configuration;
