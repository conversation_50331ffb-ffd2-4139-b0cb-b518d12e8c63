import axios, { type AxiosResponse } from 'axios';

export interface LogEntry {
  id: string;
  timestamp: string;
  message: string;
  level: string;
  source: string;
  resourceId: string;
  resourceType: string;
}

export interface LogsResponse {
  logs: LogEntry[];
  hasMore: boolean;
  nextStartTime?: string;
  nextEndTime?: string;
}

export interface Service {
  id: string;
  name: string;
  type: string;
  status: string;
  region: string;
  createdAt: string;
  updatedAt: string;
}

export interface ServicesResponse {
  services: Service[];
}

export interface RenderApiConfig {
  apiKey: string;
  baseUrl?: string;
}

class RenderApiService {
  private apiKey: string;
  private baseUrl: string;
  private axiosInstance;

  constructor(config: RenderApiConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://api.render.com/v1';
    
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });
  }

  // Update API key
  updateApiKey(apiKey: string): void {
    this.apiKey = apiKey;
    this.axiosInstance.defaults.headers['Authorization'] = `Bearer ${apiKey}`;
  }

  // Get all services
  async getServices(): Promise<Service[]> {
    try {
      const response: AxiosResponse<ServicesResponse> = await this.axiosInstance.get('/services');
      return response.data.services || [];
    } catch (error) {
      console.error('Error fetching services:', error);
      throw new Error('Failed to fetch services. Please check your API key and try again.');
    }
  }

  // Get logs for specific resources
  async getLogs(params: {
    resourceIds?: string[];
    startTime?: string;
    endTime?: string;
    limit?: number;
    direction?: 'forward' | 'backward';
    level?: string;
    search?: string;
  }): Promise<LogsResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.resourceIds && params.resourceIds.length > 0) {
        params.resourceIds.forEach(id => queryParams.append('resourceId', id));
      }
      
      if (params.startTime) {
        queryParams.append('startTime', params.startTime);
      }
      
      if (params.endTime) {
        queryParams.append('endTime', params.endTime);
      }
      
      if (params.limit) {
        queryParams.append('limit', params.limit.toString());
      }
      
      if (params.direction) {
        queryParams.append('direction', params.direction);
      }
      
      if (params.level) {
        queryParams.append('level', params.level);
      }
      
      if (params.search) {
        queryParams.append('search', params.search);
      }

      const response: AxiosResponse<LogsResponse> = await this.axiosInstance.get(
        `/logs?${queryParams.toString()}`
      );
      
      return response.data;
    } catch (error) {
      console.error('Error fetching logs:', error);
      throw new Error('Failed to fetch logs. Please check your configuration and try again.');
    }
  }

  // Get logs with real-time updates (polling)
  async subscribeToLogs(
    params: {
      resourceIds?: string[];
      level?: string;
      search?: string;
    },
    callback: (logs: LogEntry[]) => void,
    intervalMs: number = 5000
  ): Promise<() => void> {
    let isActive = true;
    let lastTimestamp: string | undefined;

    const poll = async () => {
      if (!isActive) return;

      try {
        const now = new Date().toISOString();
        const startTime = lastTimestamp || new Date(Date.now() - 60000).toISOString(); // Last minute if no previous timestamp

        const response = await this.getLogs({
          ...params,
          startTime,
          endTime: now,
          direction: 'forward',
          limit: 100,
        });

        if (response.logs && response.logs.length > 0) {
          callback(response.logs);
          lastTimestamp = response.logs[response.logs.length - 1].timestamp;
        }
      } catch (error) {
        console.error('Error in log subscription:', error);
      }

      if (isActive) {
        setTimeout(poll, intervalMs);
      }
    };

    // Start polling
    poll();

    // Return unsubscribe function
    return () => {
      isActive = false;
    };
  }

  // Test API connection
  async testConnection(): Promise<boolean> {
    try {
      await this.getServices();
      return true;
    } catch (error) {
      return false;
    }
  }
}

export default RenderApiService;
