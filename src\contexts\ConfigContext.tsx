import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import RenderApiService from '../services/renderApi';

interface ConfigContextType {
  apiKey: string;
  setApiKey: (key: string) => void;
  renderApi: RenderApiService | null;
  isConfigured: boolean;
  selectedServices: string[];
  setSelectedServices: (services: string[]) => void;
  autoRefresh: boolean;
  setAutoRefresh: (enabled: boolean) => void;
  refreshInterval: number;
  setRefreshInterval: (interval: number) => void;
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

interface ConfigProviderProps {
  children: ReactNode;
}

export const ConfigProvider: React.FC<ConfigProviderProps> = ({ children }) => {
  const [apiKey, setApiKeyState] = useState<string>('');
  const [renderApi, setRenderApi] = useState<RenderApiService | null>(null);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);
  const [refreshInterval, setRefreshInterval] = useState<number>(5000); // 5 seconds

  // Load configuration from localStorage on mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('render-api-key');
    const savedServices = localStorage.getItem('render-selected-services');
    const savedAutoRefresh = localStorage.getItem('render-auto-refresh');
    const savedRefreshInterval = localStorage.getItem('render-refresh-interval');

    // Use saved API key or fall back to environment variable
    if (savedApiKey) {
      setApiKeyState(savedApiKey);
    } else if (import.meta.env.VITE_RENDER_API_KEY) {
      setApiKeyState(import.meta.env.VITE_RENDER_API_KEY);
    }

    if (savedServices) {
      try {
        setSelectedServices(JSON.parse(savedServices));
      } catch (error) {
        console.error('Error parsing saved services:', error);
      }
    }

    // Use saved auto-refresh setting or fall back to environment variable
    if (savedAutoRefresh) {
      setAutoRefresh(savedAutoRefresh === 'true');
    } else if (import.meta.env.VITE_DEFAULT_AUTO_REFRESH !== undefined) {
      setAutoRefresh(import.meta.env.VITE_DEFAULT_AUTO_REFRESH === 'true');
    }

    // Use saved refresh interval or fall back to environment variable
    if (savedRefreshInterval) {
      const interval = parseInt(savedRefreshInterval, 10);
      if (!isNaN(interval) && interval >= 1000) {
        setRefreshInterval(interval);
      }
    } else if (import.meta.env.VITE_DEFAULT_REFRESH_INTERVAL) {
      const envInterval = parseInt(import.meta.env.VITE_DEFAULT_REFRESH_INTERVAL, 10);
      if (!isNaN(envInterval) && envInterval >= 1000) {
        setRefreshInterval(envInterval);
      }
    }
  }, []);

  // Update API service when API key changes
  useEffect(() => {
    if (apiKey) {
      const api = new RenderApiService({ apiKey });
      setRenderApi(api);
    } else {
      setRenderApi(null);
    }
  }, [apiKey]);

  const setApiKey = (key: string) => {
    setApiKeyState(key);
    if (key) {
      localStorage.setItem('render-api-key', key);
    } else {
      localStorage.removeItem('render-api-key');
    }
  };

  const setSelectedServicesWithStorage = (services: string[]) => {
    setSelectedServices(services);
    localStorage.setItem('render-selected-services', JSON.stringify(services));
  };

  const setAutoRefreshWithStorage = (enabled: boolean) => {
    setAutoRefresh(enabled);
    localStorage.setItem('render-auto-refresh', enabled.toString());
  };

  const setRefreshIntervalWithStorage = (interval: number) => {
    setRefreshInterval(interval);
    localStorage.setItem('render-refresh-interval', interval.toString());
  };

  const value: ConfigContextType = {
    apiKey,
    setApiKey,
    renderApi,
    isConfigured: !!apiKey && !!renderApi,
    selectedServices,
    setSelectedServices: setSelectedServicesWithStorage,
    autoRefresh,
    setAutoRefresh: setAutoRefreshWithStorage,
    refreshInterval,
    setRefreshInterval: setRefreshIntervalWithStorage,
  };

  return (
    <ConfigContext.Provider value={value}>
      {children}
    </ConfigContext.Provider>
  );
};

export const useConfig = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
};
